#!/usr/bin/env python3
"""
Split the 200k.txt file into different sizes for benchmarking.
"""
import os
import logging


def split_file(source_file: str, output_sizes: list[tuple[int, str]]) -> None:
    """Split source file into multiple files with different line counts."""
    logging.info(f"Reading source file: {source_file}")

    # Read all lines from source file
    with open(source_file, 'r') as f:
        lines = f.readlines()

    total_lines = len(lines)
    logging.info(f"Total lines in source file: {total_lines:,}")

    # Create output directory if it doesn't exist
    os.makedirs('test_data', exist_ok=True)

    # Create files with different sizes
    for size, filename in output_sizes:
        output_path = os.path.join('test_data', filename)
        with open(output_path, 'w') as f:
            f.writelines(lines[:size])
        logging.info(f"Generated {output_path} with {size:,} lines")


def main() -> None:
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

    # Define output sizes and filenames
    sizes = [
        (10000, 'bench_10000.txt'),
        (50000, 'bench_50000.txt'),
        (100000, 'bench_100000.txt'),
        (250000, 'bench_250000.txt')
    ]

    source_file = 'test_data/200k.txt'
    split_file(source_file, sizes)


if __name__ == '__main__':
    main()
