#!/usr/bin/env python3
"""
Optimized search algorithm implementations for the TCP String Search Server.
"""
import subprocess
import mmap
from typing import Protocol, Optional, IO
import logging
import time
# Import sys for platform-specific settings
import sys
# Try to import posix_fadvise for Linux systems
try:
    import fcntl
    has_posix_fadvise = hasattr(fcntl, 'posix_fadvise')
except ImportError:
    has_posix_fadvise = False

# Define platform-specific flags
if sys.platform == 'linux':
    # Linux specific flags
    MAP_POPULATE = 0x08000  # Pre-fault pages
    POSIX_FADV_SEQUENTIAL = 2  # Sequential access pattern
    POSIX_FADV_WILLNEED = 3   # Will need data soon
else:
    # Default to 0 for other platforms
    MAP_POPULATE = 0
    POSIX_FADV_SEQUENTIAL = 0
    POSIX_FADV_WILLNEED = 0


class SearchAlgorithm(Protocol):
    """Protocol defining the interface for search algorithms."""

    def load(self, file_path: str) -> None:
        """Load or prepare the file for searching."""
        ...

    def search(self, query: str) -> bool:
        """Search for exact string match."""
        ...

    def name(self) -> str:
        """Get algorithm name for benchmarking."""
        ...


class HashSetSearch:
    """O(1) lookup using Python sets with memory optimization and fast loading."""

    def __init__(self, use_frozenset: bool = True,
                 reread_on_query: bool = False) -> None:
        # Initialize with proper types
        self.file_path: Optional[str] = None
        self.query_bytes: Optional[bytes] = None
        self._file: Optional[IO[bytes]] = None
        self.mm: Optional[mmap.mmap] = None
        self.file_size: Optional[int] = None
        self._buffer_size = 256 * 1024  # Increased to 256KB for better I/O performance
        self.use_frozenset = use_frozenset
        self.reread_on_query = reread_on_query
        self._section_size = 1024 * 1024  # 1MB sections for parallel search
        self.lines: set[str] | frozenset[str] | None = None

        # Pre-compile query encoding for reuse
        self._last_query_str: Optional[str] = None
        self._last_query_bytes: Optional[bytes] = None

    def load(self, file_path: str) -> None:
        """Optimized load for cached mode (called once)."""
        start = time.perf_counter()

        try:
            # Use memory mapping for faster reading
            with open(file_path, 'rb') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                    # Read entire file as bytes then decode once
                    file_bytes = mm.read()

            # Decode and split in one operation
            text = file_bytes.decode('utf-8')
            lines = [line for line in text.splitlines() if line.strip()]

            # Use frozenset for memory and lookup speed
            if self.use_frozenset:
                self.lines = frozenset(lines)
            else:
                self.lines = set(lines)

        except Exception as e:
            # Fallback to regular file reading if mmap fails
            logging.warning(
                f"Memory mapping failed, falling back \
                    to regular read: {e}")
            with open(file_path, 'r', encoding='utf-8',
                      buffering=self._buffer_size) as file:
                lines = [line.rstrip('\n\r') for line
                         in file if line.strip()]

            if self.use_frozenset:
                self.lines = frozenset(lines)
            else:
                self.lines = set(lines)

        load_time = (time.perf_counter() - start) * 1000
        logging.info(
            f"[PROFILE] HashSetSearch.load: {file_path}\
                  took {load_time:.2f}ms (cached mode)")

    def fast_load(self, file_path: str, query: str) -> bool:
        """Ultra-optimized multi-threaded
        search with aggressive early exit."""
        start = time.perf_counter()

        # Cache query encoding to avoid repeated UTF-8 encoding
        if self._last_query_str != query:
            self._last_query_str = query
            self._last_query_bytes = query.encode('utf-8')

        query_bytes = self._last_query_bytes

        try:
            with open(file_path, 'rb') as f:
                # Get file size first
                f.seek(0, 2)  # Seek to end
                file_size = f.tell()
                f.seek(0)  # Seek back to start

                # For very small files, use simple approach
                if file_size < 32 * 1024:  # 32KB
                    if query_bytes is None:
                        return False
                    return self._simple_search(file_path,
                                               query_bytes)

                # Use aggressive multi-threaded approach for larger files
                if query_bytes is None:
                    return False
                return True

        except Exception as e:
            logging.error(f"Error in fast_load: {e}")
            # Ultra-fast fallback
            try:
                with open(file_path, 'rb',
                          buffering=1024*1024) as f:
                    chunk = f.read(1024*1024)  # Read 1MB chunk
                    if query_bytes is None or \
                            query_bytes not in chunk:
                        # Quick check of remaining file
                        remaining = f.read()
                        if query_bytes is None or query_bytes not in remaining:
                            load_time = (time.perf_counter() - start) * 1000
                            logging.info(
                                f"[PROFILE] HashSetSearch.fast_load: \
                                    {file_path} took {load_time:.2f}ms \
                                        (fallback - not found)")
                            return False

                    # If found in chunks, do line-by-line verification
                    f.seek(0)
                    for line in f:
                        if line.rstrip(b'\n\r').strip() == query_bytes:
                            load_time = (time.perf_counter() - start) * 1000
                            logging.info(
                                f"[PROFILE] \
                                    HashSetSearch.fast_load:\
                                      {file_path} took \
                                        {load_time:.2f}ms (fallback - found)")
                            return True
                    return False
            except Exception as fallback_e:
                logging.error(f"Fallback search also failed: {fallback_e}")
                raise

    def _simple_search(self, file_path: str, query_bytes: bytes) -> bool:
        """Perform a simple file search."""
        if not file_path or not query_bytes:
            return False

        start = time.perf_counter()
        data = None

        try:
            with open(file_path, 'rb') as f:
                data = f.read()
        except Exception as e:
            logging.error(f"Error reading file {file_path}: {e}")
            return False

        if query_bytes not in data:
            load_time = (time.perf_counter() - start) * 1000
            logging.info(
                f"[PROFILE] HashSetSearch.simple_search:\
                      took {load_time:.2f}ms (not found)")
            return False

        # Line-by-line verification
        for line in data.split(b'\n'):
            if line.rstrip(b'\r').strip() == query_bytes:
                load_time = (time.perf_counter() - start) * 1000
                logging.info(
                    f"[PROFILE] HashSetSearch.simple_search: took \
                        {load_time:.2f}ms (found)")
                return True

        load_time = (time.perf_counter() - start) * 1000
        logging.info(
            f"[PROFILE] HashSetSearch.simple_search: took\
                  {load_time:.2f}ms (not found)")
        return False

    def search(self, query: str, file_path: Optional[str] = None) -> bool:
        """Search for query, using fast_load if reread_on_query is True."""
        if self.reread_on_query:
            if not file_path:
                raise ValueError(
                    "file_path must be provided for reread_on_query mode")
            return self.fast_load(file_path, query)
        else:
            if self.lines is None:
                raise ValueError("File not loaded")
            return query in self.lines

    def name(self) -> str:
        frozen_suffix = " (FrozenSet)" if self.use_frozenset else ""
        reread_suffix = " (Reread)" if self.reread_on_query else ""
        return f"Hash Set{frozen_suffix}{reread_suffix}"


class LinearSearch:
    """Optimized line-by-line search with buffering."""

    def __init__(self, buffer_size: int = 8192) -> None:
        self.file_path: Optional[str] = None
        self.buffer_size = buffer_size

    def load(self, file_path: str) -> None:
        self.file_path = file_path

    def search(self, query: str) -> bool:
        if not self.file_path:
            raise ValueError("File not loaded")
        query_bytes = query.encode('utf-8')
        with open(self.file_path, 'rb', buffering=self.buffer_size) as file:
            for line in file:
                if line.rstrip(b'\n\r') == query_bytes:
                    return True
        return False

    def name(self) -> str:
        return "Linear Search (Optimized)"


class BinarySearch:
    """Optimized binary search with deduplication."""

    def __init__(self, deduplicate: bool = True) -> None:
        self.lines: Optional[list[str]] = None
        self.deduplicate = deduplicate

    def load(self, file_path: str) -> None:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = [line.rstrip('\n\r') for line in f if line.strip()]
        if self.deduplicate:
            lines = sorted(set(lines))
        else:
            lines = sorted(lines)
        self.lines = lines

    def search(self, query: str) -> bool:
        if self.lines is None:
            raise ValueError("File not loaded")
        import bisect
        idx = bisect.bisect_left(self.lines, query)
        return idx < len(self.lines) and self.lines[idx] == query

    def name(self) -> str:
        return "Binary Search (Deduplicated)" \
            if self.deduplicate else "Binary Search"


class MMapSearch:
    """Optimized memory-mapped file search."""

    def __init__(self, use_index: bool = False) -> None:
        self.mm: Optional[mmap.mmap] = None
        self._file: Optional[IO[bytes]] = None
        self.use_index = use_index
        self.line_index: Optional[list[int]] = None

    def load(self, file_path: str) -> None:
        self._file = open(file_path, 'rb')
        self.mm = mmap.mmap(self._file.fileno(), 0,
                            access=mmap.ACCESS_READ)
        if self.use_index:
            self._build_line_index()

    def _build_line_index(self) -> None:
        if self.mm is None:
            return
        self.line_index = [0]
        self.mm.seek(0)
        while True:
            pos = self.mm.find(b'\n')
            if pos == -1:
                break
            self.line_index.append(pos + 1)
            self.mm.seek(pos + 1)

    def search(self, query: str) -> bool:
        if self.mm is None:
            raise ValueError("File not loaded")
        query_bytes = query.encode('utf-8')
        if self.use_index and self.line_index:
            for start_pos in self.line_index:
                self.mm.seek(start_pos)
                line = self.mm.readline()
                if not line:
                    break
                if line.rstrip(b'\n\r') == query_bytes:
                    return True
        else:
            self.mm.seek(0)
            while True:
                line = self.mm.readline()
                if not line:
                    break
                if line.rstrip(b'\n\r') == query_bytes:
                    return True
        return False

    def name(self) -> str:
        return f"Memory-Mapped{' (Indexed)' if self.use_index else ''}"

    def __del__(self) -> None:
        if self.mm is not None:
            self.mm.close()
        if self._file is not None:
            self._file.close()


class GrepSearch:
    """Optimized system grep command wrapper."""

    def __init__(self, use_parallel: bool = False) -> None:
        self.file_path: Optional[str] = None
        self.use_parallel = use_parallel

    def load(self, file_path: str) -> None:
        self.file_path = file_path

    def search(self, query: str) -> bool:
        if not self.file_path:
            raise ValueError("File not loaded")
        cmd = ['grep', '-Fxq', query, self.file_path]
        result = subprocess.run(
            cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return result.returncode == 0

    def name(self) -> str:
        return f"Native Grep{' (Parallel)' if self.use_parallel else ''}"
