#!/usr/bin/env python3
"""
Generate comprehensive speed report comparing different search algorithms.
"""
from typing import Protocol
from typing import Any
import time
import logging
import matplotlib.pyplot as plt
import pandas as pd
import os
from typing import List, Dict, Optional, Tuple
from search_algorithms_new import HashSetSearch, LinearSearch, \
    BinarySearch, MMapSearch, GrepSearch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


class SearchAlgorithm(Protocol):
    def load(self, file_path: str) -> None: ...
    def search(self, query: str,
               file_path: 'Optional[str]' = None) -> bool: ...

    def name(self) -> str: ...


def benchmark_algorithm(algo: SearchAlgorithm,
                        file_path: str, queries: List[str],
                        iterations: int = 3) -> Dict[str, Any]:
    """Benchmark a single algorithm and return metrics."""
    # Measure load time
    start = time.perf_counter()
    algo.load(file_path)
    load_time = (time.perf_counter() - start) * 1000  # ms

    search_times: List[float] = []
    found_count = 0

    # Test each query multiple times
    for query in queries:
        query_times: List[float] = []
        for _ in range(iterations):
            start = time.perf_counter()
            # Handle reread_on_query mode specially
            if getattr(algo, 'reread_on_query', False):
                found = algo.search(query, file_path=file_path)
            else:
                found = algo.search(query)
            duration = (time.perf_counter() - start) * 1000  # ms
            query_times.append(duration)
            if found:
                found_count += 1
        search_times.extend(query_times)

    return {
        'Algorithm': algo.name(),
        'Load Time (ms)': load_time,
        'Min Search Time (ms)': min(search_times),
        'Max Search Time (ms)': max(search_times),
        'Avg Search Time (ms)': sum(search_times) / len(search_times)
    }


def create_test_queries(file_path: str, count: int = 5) -> List[str]:
    """Create a mix of existing and non-existing test queries."""
    existing_lines = []
    with open(file_path, 'r') as f:
        # Sample some actual lines from the file
        lines = f.readlines()
        if lines:
            import random
            existing_lines = random.sample(lines, min(3, len(lines)))

    # Add some non-existing queries
    nonexisting = [
        "nonexistent_string_12345",
        "this_should_not_be_found"
    ]

    return [line.strip() for line in existing_lines] + nonexisting


def plot_comparison(data: pd.DataFrame) -> None:
    """Generate performance comparison plots."""
    if data.empty:
        logging.warning("No data to plot")
        return

    file_sizes = ['10K', '50K', '100K', '250K']
    metrics = [
        ('Load Time (ms)', 'Load Time vs File Size'),
        ('Avg Search Time (ms)', 'Average Search Time vs File Size')
    ]

    for metric, title in metrics:
        plt.figure(figsize=(12, 6))

        algos = data['Algorithm'].unique()
        if len(algos) == 0:
            # Create a default empty plot
            plt.plot([], [], marker='o', label='No data')

        for algo in algos:
            algo_data = data[data['Algorithm'] == algo].copy()
            algo_data = algo_data.set_index('Size Label')
            if not algo_data.empty:
                plot_x = []
                plot_y = []
                for size in file_sizes:
                    if size in algo_data.index:
                        plot_x.append(size)
                        plot_y.append(algo_data.loc[size, metric])
                plt.plot(plot_x, plot_y, marker='o', label=algo)

        plt.title(title)
        plt.xlabel('File Size')
        plt.ylabel(metric)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(
            f'docs/{metric.lower().replace(" ", "_")}_chart.png', bbox_inches='tight')
        plt.close()


# Default test files configuration
DEFAULT_TEST_FILES = [
    ("test_data/bench_10000.txt", "10K"),
    ("test_data/bench_50000.txt", "50K"),
    ("test_data/bench_100000.txt", "100K"),
    ("test_data/bench_250000.txt", "250K")
]


def generate_report(test_files: Optional[List[Tuple[str, str]]] = None) -> None:
    """Generate comprehensive speed report.

    Args:
        test_files: Optional list of (file_path, size_label) tuples.
                   If None, uses DEFAULT_TEST_FILES.
    """
    if test_files is None:
        test_files = DEFAULT_TEST_FILES

    results = []
    algorithms = [
        HashSetSearch(use_frozenset=True),
        HashSetSearch(use_frozenset=True, reread_on_query=True),
        LinearSearch(),
        BinarySearch(deduplicate=True),
        MMapSearch(),
        GrepSearch()
    ]

    # Create docs directory if it doesn't exist
    os.makedirs('docs', exist_ok=True)

    # Run benchmarks
    for file_path, size_label in test_files:
        if not os.path.exists(file_path):
            logging.warning(f"File not found: {file_path}")
            continue

        logging.info(f"\nBenchmarking file size {size_label}")
        test_queries = create_test_queries(file_path)

        for algo in algorithms:
            logging.info(f"Testing {getattr(algo, 'name')()}")
            metrics = benchmark_algorithm(algo, file_path, test_queries) # type: ignore
            metrics['Size Label'] = size_label
            metrics['File Path'] = file_path
            results.append(metrics)

            logging.info(
                f"{algo.name()}: " # type: ignore
                f"Load={metrics['Load Time (ms)']:.2f}ms, "
                f"Avg Search={metrics['Avg Search Time (ms)']:.2f}ms"
            )

    # Create DataFrame
    df = pd.DataFrame(results)

    if df.empty:
        logging.warning(
            "No data to generate report - skipping plots and pivot tables")
        return

    # Save detailed results
    df.to_csv('docs/benchmark_results_full.csv', index=False)

    # Generate plots
    plot_comparison(df)

    # Generate markdown report
    with open('docs/speed_report.md', 'w') as f:
        f.write('# Search Algorithm Performance Report\n\n')

        # Performance summary tables
        report_metrics: List[Tuple[str, str]] = [
            ('Avg Search Time (ms)', 'Average Search Time'),
            ('Load Time (ms)', 'Load Time')
        ]

        f.write('## Performance Summary\n\n')

        for metric_name, title in report_metrics:
            f.write(f'### {title} by File Size\n\n')
            pivot_table = df.pivot_table(
                index='Algorithm',
                columns='Size Label',
                values=metric_name,
                aggfunc='mean'
            ).reindex(columns=['10K', '50K', '100K', '250K'])

            if 'Time' in metric_name:
                pivot_table = pivot_table.round(3)
            else:
                pivot_table = pivot_table.round(1)

            f.write(pivot_table.to_markdown())
            f.write('\n\n')

        # Add visualization references
        f.write('## Performance Visualizations\n\n')
        f.write('### Load Time Comparison\n')
        f.write('![Load Time Comparison](load_time_(ms)_chart.png)\n\n')
        f.write('### Search Time Comparison\n')
        f.write('![Search Time Comparison](avg_search_time_(ms)_chart.png)\n\n')

        # Algorithm characteristics
        f.write('## Algorithm Characteristics\n\n')
        f.write('1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off\n')
        f.write(
            '2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,\
                but slower search\n')
        f.write(
            '3. **Linear Search**: Simple implementation, \
                high time complexity (O(n))\n')
        f.write('4. **Binary Search**: O(log n) search with sorting overhead\n')
        f.write('5. **Memory-Mapped**: Efficient for large files\n')
        f.write('6. **Native Grep**: System-level optimization\n')


if __name__ == "__main__":
    logging.info("Generating speed report...")
    generate_report()
    logging.info(
        "Speed report generation complete. Check docs/ folder for results.")
